export const formatSteps = (steps: number): string => {
  if (steps >= 1000000) {
    return `${(steps / 1000000).toFixed(1)}M`;
  }
  if (steps >= 1000) {
    return `${(steps / 1000).toFixed(1)}k`;
  }
  return steps.toString();
};

export const calculateDistance = (steps: number): number => {
  // Average step length is about 0.7 meters
  const meters = steps * 0.7;
  return meters / 1000; // Convert to kilometers
};

export const calculateCalories = (steps: number, weight: number = 70): number => {
  // Rough calculation: 0.04 calories per step for average person
  return Math.round(steps * 0.04);
};

export const getStepGoal = (period: string): number => {
  switch (period) {
    case 'day': return 10000;
    case 'week': return 70000;
    case 'month': return 300000;
    default: return 10000;
  }
};