import { describe, it, expect } from 'vitest';
import { formatSteps, calculateDistance, calculateCalories, getStepGoal } from '../utils/stepUtils';

describe('stepUtils', () => {
  it('formatSteps should format numbers with k and M', () => {
    expect(formatSteps(500)).toBe('500');
    expect(formatSteps(1500)).toBe('1.5k');
    expect(formatSteps(2500000)).toBe('2.5M');
  });

  it('calculateDistance should return kilometers', () => {
    expect(calculateDistance(1000)).toBeCloseTo(0.7);
    expect(calculateDistance(0)).toBeCloseTo(0);
  });

  it('calculateCalories should estimate calories', () => {
    expect(calculateCalories(1000)).toBe(40);
    expect(calculateCalories(0)).toBe(0);
  });

  it('getStepGoal returns sensible defaults', () => {
    expect(getStepGoal('day')).toBe(10000);
    expect(getStepGoal('week')).toBe(70000);
    expect(getStepGoal('month')).toBe(300000);
    expect(getStepGoal('unknown')).toBe(10000);
  });
});
