import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Activity } from 'lucide-react-native';
import * as Progress from 'react-native-progress';
import { formatSteps } from '../utils/stepUtils';

interface StepCardProps {
  steps: number;
  goal: number;
  period: string;
}

export function StepCard({ steps, goal, period }: StepCardProps) {
  const progress = goal > 0 ? Math.min(steps / goal, 1) : 0;
  const percentage = Math.round(progress * 100);

  const remaining = Math.max(0, goal - steps);

  const getPeriodLabel = () => {
    switch (period) {
      case 'day': return 'Today';
      case 'week': return 'This Week';
      case 'month': return 'This Month';
      default: return 'Today';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Activity size={28} color="#10B981" />
        </View>
        <View style={styles.headerText}>
          <Text style={styles.title}>Steps {getPeriodLabel()}</Text>
          <Text style={styles.subtitle}>{formatSteps(steps)} / {formatSteps(goal)}</Text>
        </View>
      </View>

      <View style={styles.progressContainer}>
        <Progress.Circle
          size={120}
          progress={progress}
          thickness={8}
          color="#10B981"
          unfilledColor="#E5E7EB"
          borderWidth={0}
          showsText={false}
        />
        <View style={styles.progressText}>
          <Text style={styles.percentageText}>{percentage}%</Text>
          <Text style={styles.progressLabel}>Complete</Text>
        </View>
      </View>

      <View style={styles.stats}>
        <View style={styles.stat}>
          <Text style={styles.statValue}>{formatSteps(steps)}</Text>
          <Text style={styles.statLabel}>Steps</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.stat}>
          <Text style={styles.statValue}>
            {remaining === 0 ? 'Goal reached' : formatSteps(remaining)}
          </Text>
          <Text style={styles.statLabel}>To Goal</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 56,
    height: 56,
    backgroundColor: '#F0FDF4',
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 24,
    position: 'relative',
  },
  progressText: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  percentageText: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  progressLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  stats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stat: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  divider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 24,
  },
});