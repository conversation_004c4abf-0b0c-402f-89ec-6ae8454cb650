import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { formatSteps } from '../utils/stepUtils';

interface ProgressChartProps {
  period: string;
}

export function ProgressChart({ period }: ProgressChartProps) {
  const getDayData = () => [
    { label: '6AM', steps: 150, hour: 6 },
    { label: '9AM', steps: 850, hour: 9 },
    { label: '12PM', steps: 2340, hour: 12 },
    { label: '3PM', steps: 4200, hour: 15 },
    { label: '6PM', steps: 6800, hour: 18 },
    { label: '9PM', steps: 8542, hour: 21 },
  ];

  const getWeekData = () => [
    { label: 'Mon', steps: 8200 },
    { label: 'Tue', steps: 9400 },
    { label: 'Wed', steps: 7800 },
    { label: 'Thu', steps: 10200 },
    { label: 'Fri', steps: 6900 },
    { label: 'Sat', steps: 5400 },
    { label: 'Sun', steps: 4400 },
  ];

  const getMonthData = () => [
    { label: 'W1', steps: 58400 },
    { label: 'W2', steps: 62100 },
    { label: 'W3', steps: 55600 },
    { label: 'W4', steps: 58467 },
  ];

  const getData = () => {
    switch (period) {
      case 'day': return getDayData();
      case 'week': return getWeekData();
      case 'month': return getMonthData();
      default: return getDayData();
    }
  };

  const data = getData();
  const maxSteps = Math.max(...data.map(d => d.steps));

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Progress Overview</Text>
        <Text style={styles.subtitle}>Your activity throughout the {period}</Text>
      </View>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chartContainer}>
        <View style={styles.chart}>
          {data.map((item, index) => {
            const height = (item.steps / maxSteps) * 100;
            return (
              <View key={index} style={styles.barContainer}>
                <View style={styles.barWrapper}>
                  <View
                    style={[
                      styles.bar,
                      { height: `${height}%` },
                    ]}
                  />
                </View>
                <Text style={styles.barLabel}>{item.label}</Text>
                <Text style={styles.barValue}>{formatSteps(item.steps)}</Text>
              </View>
            );
          })}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    marginTop: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  chartContainer: {
    marginHorizontal: -8,
  },
  chart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 120,
    paddingHorizontal: 8,
    gap: 12,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 40,
  },
  barWrapper: {
    height: 80,
    width: 16,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginBottom: 2,
  },
  barValue: {
    fontSize: 10,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
});