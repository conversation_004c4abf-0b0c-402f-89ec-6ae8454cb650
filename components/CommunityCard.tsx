import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { Users, Crown, Trophy, Medal, Activity } from 'lucide-react-native';
import { formatSteps } from '../utils/stepUtils';
import { joinCommunity } from '@/src/api/communities';
import { supabase } from '@/src/lib/supabaseClient';

interface Community {
  id: string;
  name: string;
  members: number;
  totalSteps: number;
  rank?: number;
  isOwner?: boolean;
  description?: string;
}

interface CommunityCardProps {
  community: Community;
  showRank?: boolean;
  showJoinButton?: boolean;
}

export function CommunityCard({ community, showRank, showJoinButton }: CommunityCardProps) {
  const [isJoined, setIsJoined] = useState(false);
  const [loadingJoin, setLoadingJoin] = useState(false);
  const [membersCount, setMembersCount] = useState<number>(community.members ?? 0);

  useEffect(() => {
    let mounted = true;
    const checkMembership = async () => {
      try {
        if (!supabase) return;
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user) return;
        const userId = session.user.id;
        const { data, error } = await supabase
          .from('community_members')
          .select('*')
          .eq('community_id', community.id)
          .eq('user_id', userId)
          .maybeSingle();
        if (error) return;
        if (mounted && data) setIsJoined(true);
      } catch {
        // ignore
      }
    };

    const fetchMemberCount = async () => {
      try {
        if (!supabase) return;
        const { data, error, count } = await supabase
          .from('community_members')
          .select('*', { count: 'exact' })
          .eq('community_id', community.id);
        if (error) return;
        if (mounted) setMembersCount(typeof count === 'number' ? count : (data?.length ?? 0));
      } catch {
        // ignore
      }
    };

    checkMembership();
    fetchMemberCount();
    return () => { mounted = false; };
  }, [community.id]);

  // use centralized formatter

  const getRankIcon = (rank?: number) => {
    switch (rank) {
      case 1: return <Crown size={20} color="#F59E0B" />;
      case 2: return <Trophy size={20} color="#6B7280" />;
      case 3: return <Medal size={20} color="#CD7C2F" />;
      default: return null;
    }
  };

  const handleJoin = async () => {
    if (isJoined || loadingJoin) return;
    setLoadingJoin(true);
    const prevJoined = isJoined;
    const prevCount = membersCount;
    // optimistic update
    setIsJoined(true);
    setMembersCount((c) => c + 1);
    try {
      await joinCommunity(community.id);
      // re-fetch to ensure accurate count
      try {
        if (supabase) {
          const { data, error, count } = await supabase
            .from('community_members')
            .select('*', { count: 'exact' })
            .eq('community_id', community.id);
          if (!error) setMembersCount(typeof count === 'number' ? count : (data?.length ?? prevCount + 1));
        }
      } catch {
        // ignore re-fetch errors
      }
    } catch (err: any) {
      console.error('Failed to join community', err);
      // rollback optimistic update
      setIsJoined(prevJoined);
      setMembersCount(prevCount);
      Alert.alert('Error', err?.message || 'Failed to join community');
    } finally {
      setLoadingJoin(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={styles.nameRow}>
            <Text style={styles.name}>{community.name}</Text>
            {community.isOwner && (
              <View style={styles.ownerBadge}>
                <Crown size={14} color="#F59E0B" />
              </View>
            )}
            {showRank && community.rank && (
              <View style={styles.rankBadge}>
                {getRankIcon(community.rank)}
              </View>
            )}
          </View>
          {community.description && (
            <Text style={styles.description}>{community.description}</Text>
          )}
        </View>
      </View>

      <View style={styles.stats}>
        <View style={styles.stat}>
          <Users size={16} color="#6B7280" />
          <Text style={styles.statText}>{membersCount} members</Text>
        </View>
        <View style={styles.stat}>
          <Activity size={16} color="#6B7280" />
          <Text style={styles.statText}>{formatSteps(community.totalSteps)} steps</Text>
        </View>
      </View>

      {showJoinButton && (
        <TouchableOpacity
          style={[styles.joinButton, (isJoined || loadingJoin) && styles.joinedButton]}
          onPress={handleJoin}
          disabled={isJoined || loadingJoin}
        >
          <View style={styles.joinButtonContent}>
            {loadingJoin && (
              <ActivityIndicator size="small" color={isJoined || loadingJoin ? '#FFFFFF' : '#FFFFFF'} style={styles.spinner} />
            )}
            <Text style={[styles.joinButtonText, (isJoined || loadingJoin) && styles.joinedButtonText]}>
              {isJoined ? 'Joined' : loadingJoin ? 'Joining...' : 'Join Community'}
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {showRank && (
        <View style={styles.rankContainer}>
          <Text style={styles.rankText}>Rank #{community.rank} in community</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  name: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
  },
  ownerBadge: {
    backgroundColor: '#FEF3C7',
    borderRadius: 12,
    padding: 4,
  },
  rankBadge: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    padding: 4,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  stats: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 16,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  joinButton: {
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  joinButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinner: {
    marginRight: 8,
  },
  joinedButton: {
    backgroundColor: '#F3F4F6',
  },
  joinButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  joinedButtonText: {
    color: '#6B7280',
  },
  rankContainer: {
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  rankText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#10B981',
  },
});