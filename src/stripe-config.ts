export interface Product {
  id: string;
  priceId: string;
  name: string;
  description: string;
  mode: 'payment' | 'subscription';
}

// Prefer environment variables for price/product IDs so they can be changed without code edits.
const PROD_DEFAULT_ID = 'prod_SyNXCKqamTt3sp';
const PRICE_DEFAULT_ID = 'price_1S2QmeLEQSxAzqUbm96mL3oj';

export const products: Product[] = [
  {
    id: process.env.EXPO_PUBLIC_STRIPE_PRODUCT_TEPPi || PROD_DEFAULT_ID,
    priceId: process.env.EXPO_PUBLIC_STRIPE_PRICE_TEPPi || PRICE_DEFAULT_ID,
    name: 'Teppi',
    description: 'Premium step tracking features',
    mode: 'payment',
  },
];

export const getProductById = (id: string): Product | undefined => {
  return products.find(product => product.id === id);
};

export const getProductByPriceId = (priceId: string): Product | undefined => {
  return products.find(product => product.priceId === priceId);
};