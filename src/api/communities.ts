import { supabase } from '@/src/lib/supabaseClient';

export async function joinCommunity(communityId: string) {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please set EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables.');
  }

  const { data: { session } } = await supabase!.auth.getSession();
  if (!session?.user) {
    throw new Error('User not authenticated');
  }

  const userId = session.user.id;

  // Attempt to insert membership; if your DB uses a different table/columns adjust accordingly.
  const { data, error } = await supabase
    .from('community_members')
    .insert({ community_id: communityId, user_id: userId })
    .select()
    .maybeSingle();

  if (error) {
    throw error;
  }

  return data;
}

export async function createCommunity(name: string, description?: string) {
  if (!supabase) {
    throw new Error('Supabase is not configured. Please set EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables.');
  }

  const { data: { session } } = await supabase!.auth.getSession();
  if (!session?.user) {
    throw new Error('User not authenticated');
  }

  const userId = session.user.id;

  const { data, error } = await supabase!
    .from('communities')
    .insert({ name, description, owner_id: userId })
    .select()
    .maybeSingle();

  if (error) throw error;

  // Optionally add the creator as a member
  try {
    await supabase.from('community_members').insert({ community_id: data.id, user_id: userId });
  } catch (e) {
    // non-critical
    console.warn('Failed to add creator as member', e);
  }

  return data;
}

