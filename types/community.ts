export interface Community {
  id: string;
  name: string;
  description?: string;
  members: number;
  totalSteps: number;
  rank?: number;
  isOwner?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CommunityMember {
  id: string;
  userId: string;
  communityId: string;
  role: 'owner' | 'admin' | 'member';
  joinedAt: string;
  totalSteps: number;
}

export interface CommunityInvite {
  id: string;
  communityId: string;
  invitedByUserId: string;
  invitedEmail: string;
  status: 'pending' | 'accepted' | 'declined';
  createdAt: string;
}