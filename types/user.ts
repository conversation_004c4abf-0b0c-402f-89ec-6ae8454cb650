export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
  stepGoal: number;
  totalSteps: number;
  currentStreak: number;
  longestStreak: number;
}

export interface StepRecord {
  id: string;
  userId: string;
  date: string;
  steps: number;
  distance: number;
  calories: number;
  activeTime: number;
  createdAt: string;
}