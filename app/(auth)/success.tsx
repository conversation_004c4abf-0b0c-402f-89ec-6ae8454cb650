import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { CircleCheck as CheckCircle, ArrowRight } from 'lucide-react-native';
import { router } from 'expo-router';

export default function SuccessScreen() {
  useEffect(() => {
    // Auto-redirect after 5 seconds
    const timer = setTimeout(() => {
      router.replace('/(tabs)');
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const handleContinue = () => {
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#10B981', '#059669']}
        style={styles.background}
      >
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <CheckCircle size={80} color="#FFFFFF" />
          </View>

          <Text style={styles.title}>Payment Successful!</Text>
          <Text style={styles.subtitle}>
            Welcome to StepTracker Premium! Your account has been upgraded and you now have access to all premium features.
          </Text>

          <View style={styles.featuresContainer}>
            <View style={styles.feature}>
              <CheckCircle size={20} color="#FFFFFF" />
              <Text style={styles.featureText}>Unlimited step tracking</Text>
            </View>
            
            <View style={styles.feature}>
              <CheckCircle size={20} color="#FFFFFF" />
              <Text style={styles.featureText}>Join unlimited communities</Text>
            </View>
            
            <View style={styles.feature}>
              <CheckCircle size={20} color="#FFFFFF" />
              <Text style={styles.featureText}>Advanced analytics</Text>
            </View>
            
            <View style={styles.feature}>
              <CheckCircle size={20} color="#FFFFFF" />
              <Text style={styles.featureText}>Achievement badges</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
            <Text style={styles.continueButtonText}>Get Started</Text>
            <ArrowRight size={20} color="#10B981" />
          </TouchableOpacity>

          <Text style={styles.autoRedirectText}>
            You&apos;ll be automatically redirected in a few seconds...
          </Text>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  iconContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 18,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 40,
  },
  featuresContainer: {
    alignSelf: 'stretch',
    marginBottom: 40,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  featureText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    marginLeft: 12,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 32,
    marginBottom: 24,
  },
  continueButtonText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#10B981',
    marginRight: 8,
  },
  autoRedirectText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
});