import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { CreditCard, Check, ArrowLeft } from 'lucide-react-native';
import { products } from '@/src/stripe-config';
import { createCheckoutSession } from '@/src/api/stripe';

export default function PaymentScreen() {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const product = products[0]; // Get the Teppi product

  const handlePayment = async () => {
    if (!product) {
      setError('Product not found');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      const { url } = await createCheckoutSession({
        priceId: product.priceId,
        mode: product.mode,
        successUrl: `${window.location.origin}/(tabs)`,
        cancelUrl: `${window.location.origin}/(auth)/payment`,
      });

      if (url) {
        window.location.href = url;
      } else {
        setError('Failed to create checkout session');
      }
    } catch (err: any) {
      setError(err.message || 'Payment failed');
    } finally {
      setProcessing(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <LinearGradient
      colors={['#3B82F6', '#10B981']}
      style={styles.container}
    >
      <View style={styles.content}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <ArrowLeft size={24} color="#FFFFFF" />
        </TouchableOpacity>

        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <CreditCard size={40} color="#FFFFFF" />
          </View>
          <Text style={styles.title}>Complete Registration</Text>
          <Text style={styles.subtitle}>Secure payment processing via Stripe</Text>
        </View>

        <View style={styles.paymentCard}>
          <View style={styles.priceHeader}>
            <Text style={styles.priceAmount}>$1.00</Text>
            <Text style={styles.priceDescription}>One-time registration fee</Text>
          </View>

          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>What you get:</Text>
            
            <View style={styles.feature}>
              <Check size={20} color="#10B981" />
              <Text style={styles.featureText}>Unlimited step tracking</Text>
            </View>
            
            <View style={styles.feature}>
              <Check size={20} color="#10B981" />
              <Text style={styles.featureText}>Join unlimited communities</Text>
            </View>
            
            <View style={styles.feature}>
              <Check size={20} color="#10B981" />
              <Text style={styles.featureText}>Advanced analytics</Text>
            </View>
            
            <View style={styles.feature}>
              <Check size={20} color="#10B981" />
              <Text style={styles.featureText}>Achievement badges</Text>
            </View>
          </View>

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          <TouchableOpacity
            style={[styles.paymentButton, processing && styles.buttonDisabled]}
            onPress={handlePayment}
            disabled={processing}
          >
            <Text style={styles.paymentButtonText}>
              {processing ? 'Processing Payment...' : 'Pay with Stripe'}
            </Text>
          </TouchableOpacity>

          <Text style={styles.securityText}>
            🔒 Secure payment processing powered by Stripe
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  paymentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  priceHeader: {
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  priceAmount: {
    fontSize: 48,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  priceDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  featuresContainer: {
    marginBottom: 32,
  },
  featuresTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    marginLeft: 12,
  },
  paymentButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  paymentButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  securityText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#DC2626',
    textAlign: 'center',
  },
});