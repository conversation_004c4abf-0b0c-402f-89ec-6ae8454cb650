import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Activity, Target, TrendingUp, Calendar, Play, Pause } from 'lucide-react-native';
import { StepCard } from '@/components/StepCard';
import { ProgressChart } from '@/components/ProgressChart';
import { useSubscription } from '@/src/hooks/useSubscription';
import { useStepTracking } from '@/hooks/useStepTracking';
import { calculateDistance, calculateCalories } from '@/utils/stepUtils';

export default function HomeScreen() {
  const { subscription } = useSubscription();
  const [selectedPeriod, setSelectedPeriod] = useState('day');

  // Use real step tracking
  const {
    steps: currentSteps,
    isTracking,
    startTracking,
    stopTracking,
    resetSteps,
    permissionStatus,
    requestPermissions
  } = useStepTracking();

  // For demo purposes, simulate weekly/monthly data based on current steps
  const getStepsForPeriod = () => {
    switch (selectedPeriod) {
      case 'day': return currentSteps;
      case 'week': return currentSteps * 7; // Simulate weekly data
      case 'month': return currentSteps * 30; // Simulate monthly data
      default: return currentSteps;
    }
  };

  const periods = [
    { key: 'day', label: 'Today', icon: Calendar },
    { key: 'week', label: 'Week', icon: TrendingUp },
    { key: 'month', label: 'Month', icon: Target },
  ];

  // Auto-start tracking when component mounts
  useEffect(() => {
    if (!isTracking && permissionStatus.granted) {
      startTracking();
    }
  }, [permissionStatus.granted]);

  const handleTrackingToggle = async () => {
    if (!isTracking) {
      if (!permissionStatus.granted) {
        const newPermissionStatus = await requestPermissions();
        if (!newPermissionStatus.granted) {
          Alert.alert(
            'Permission Required',
            'Motion permission is required to track your steps. Please enable it in your device settings.',
            [{ text: 'OK' }]
          );
          return;
        }
      }
      startTracking();
    } else {
      stopTracking();
    }
  };

  const getGoal = () => {
    switch (selectedPeriod) {
      case 'day': return 10000;
      case 'week': return 70000;
      case 'month': return 300000;
      default: return 10000;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#10B981', '#059669']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.greeting}>Good Morning!</Text>
          <Text style={styles.userName}>Ready to crush your goals?</Text>
          {subscription && (
            <Text style={styles.planText}>Teppi Premium Active</Text>
          )}
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.periodSelector}>
          {periods.map((period) => (
            <TouchableOpacity
              key={period.key}
              style={[
                styles.periodButton,
                selectedPeriod === period.key && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod(period.key)}
            >
              <period.icon 
                size={20} 
                color={selectedPeriod === period.key ? '#FFFFFF' : '#6B7280'} 
              />
              <Text
                style={[
                  styles.periodButtonText,
                  selectedPeriod === period.key && styles.periodButtonTextActive,
                ]}
              >
                {period.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <StepCard
          steps={getStepsForPeriod()}
          goal={getGoal()}
          period={selectedPeriod}
        />

        {/* Step Tracking Controls */}
        <View style={styles.trackingControls}>
          <TouchableOpacity
            style={[styles.trackingButton, isTracking && styles.trackingButtonActive]}
            onPress={handleTrackingToggle}
          >
            {isTracking ? (
              <Pause size={20} color="#FFFFFF" />
            ) : (
              <Play size={20} color="#FFFFFF" />
            )}
            <Text style={styles.trackingButtonText}>
              {isTracking ? 'Pause Tracking' : 'Start Tracking'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.resetButton}
            onPress={resetSteps}
          >
            <Text style={styles.resetButtonText}>Reset Steps</Text>
          </TouchableOpacity>
        </View>

        <ProgressChart period={selectedPeriod} />

        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Activity size={24} color="#10B981" />
            </View>
            <Text style={styles.statValue}>
              {calculateDistance(getStepsForPeriod()).toFixed(1)}km
            </Text>
            <Text style={styles.statLabel}>Distance</Text>
          </View>

          <View style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <TrendingUp size={24} color="#3B82F6" />
            </View>
            <Text style={styles.statValue}>
              {calculateCalories(getStepsForPeriod())}
            </Text>
            <Text style={styles.statLabel}>Calories</Text>
          </View>

          <View style={styles.statCard}>
            <View style={styles.statIconContainer}>
              <Target size={24} color="#F97316" />
            </View>
            <Text style={styles.statValue}>
              {isTracking ? 'Active' : 'Paused'}
            </Text>
            <Text style={styles.statLabel}>Status</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    paddingTop: 16,
  },
  greeting: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  userName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginTop: 4,
  },
  planText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
    marginTop: 24,
    marginBottom: 24,
  },
  periodButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  periodButtonActive: {
    backgroundColor: '#10B981',
  },
  periodButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 24,
    marginBottom: 32,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    backgroundColor: '#F3F4F6',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  trackingControls: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 16,
  },
  trackingButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6B7280',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  trackingButtonActive: {
    backgroundColor: '#10B981',
  },
  trackingButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  resetButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  resetButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
});