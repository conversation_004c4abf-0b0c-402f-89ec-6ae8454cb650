import { useState, useEffect, useRef } from 'react';
import { useMotionPermissions } from './useMotionPermissions';

/**
 * Step tracking hook.
 *
 * Behavior:
 * - Requests motion permissions before starting tracking
 * - Attempts to use the device accelerometer (expo-sensors) to detect steps via a simple
 *   peak-detection/high-pass filter algorithm.
 * - If `expo-sensors` is not available or an error occurs, falls back to the previous
 *   simulated interval so the UI still shows progress for demos.
 *
 * Note: For production-grade step counting prefer native pedometer APIs
 * (HealthKit / Google Fit) or a well-tested library; this is a lightweight client-side
 * heuristic that works reasonably well for simple apps.
 */
export function useStepTracking(debugMode: boolean = false) {
  const [steps, setSteps] = useState(0);
  const [isTracking, setIsTracking] = useState(false);
  const [permissionRequested, setPermissionRequested] = useState(false);

  const { permissionStatus, requestPermissions } = useMotionPermissions();

  const accelSubRef = useRef<any>(null);
  const gravityRef = useRef<number>(0);
  const lastStepTimeRef = useRef<number>(0);
  const simIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Enhanced step detection state
  const movingAverageRef = useRef<number[]>([]);
  const peakCountRef = useRef<number>(0);
  const lastPeakTimeRef = useRef<number>(0);
  const stepBufferRef = useRef<number[]>([]);

  useEffect(() => {
    let mounted = true;

    const startSimulation = () => {
      if (simIntervalRef.current) return;
      simIntervalRef.current = setInterval(() => {
        // keep the old demo behaviour as a fallback
        setSteps(prev => prev + Math.floor(Math.random() * 5));
      }, 2000);
    };

    const stopSimulation = () => {
      if (simIntervalRef.current) {
        clearInterval(simIntervalRef.current as any);
        simIntervalRef.current = null;
      }
    };

    const startAccelerometer = async () => {
      try {
        // Check and request permissions first
        if (!permissionStatus.granted && !permissionRequested) {
          setPermissionRequested(true);
          const newPermissionStatus = await requestPermissions();
          if (!newPermissionStatus.granted) {
            console.log('Motion permission denied, falling back to simulation');
            startSimulation();
            return;
          }
        }

        // Check if we're on web - if so, skip accelerometer and use simulation
        if (typeof window !== 'undefined' && window.navigator) {
          console.log('Running on web, using simulation mode');
          startSimulation();
          return;
        }

        const accModule = await import('expo-sensors');
        const { Accelerometer } = accModule;

        if (!Accelerometer || typeof Accelerometer.addListener !== 'function') {
          console.log('Accelerometer not available, falling back to simulation');
          startSimulation();
          return;
        }

        // Test if we can actually use the accelerometer
        try {
          // sample ~10Hz
          if (typeof Accelerometer.setUpdateInterval === 'function') {
            Accelerometer.setUpdateInterval(100);
          }

          accelSubRef.current = Accelerometer.addListener((data: { x: number; y: number; z: number }) => {
            if (!mounted) return;

            const { x, y, z } = data;
            const magnitude = Math.sqrt(x * x + y * y + z * z);
            const now = Date.now();

            // Enhanced step detection algorithm

            // 1. Initialize gravity estimate if not set
            if (gravityRef.current === 0) {
              gravityRef.current = magnitude;
              return;
            }

            // 2. Low-pass filter for gravity estimation (slower adaptation)
            const alpha = 0.95; // Higher alpha = slower adaptation to changes
            gravityRef.current = alpha * gravityRef.current + (1 - alpha) * magnitude;

            // 3. High-pass filtered signal (remove gravity)
            const acceleration = magnitude - gravityRef.current;

            // 4. Moving average for noise reduction
            movingAverageRef.current.push(acceleration);
            if (movingAverageRef.current.length > 5) {
              movingAverageRef.current.shift();
            }

            const smoothedAcceleration = movingAverageRef.current.reduce((a, b) => a + b, 0) / movingAverageRef.current.length;

            // 5. Enhanced thresholds and conditions
            const STEP_THRESHOLD = 1.2; // Increased threshold to reduce false positives
            const MIN_STEP_INTERVAL = 300; // Minimum time between steps (ms)
            const MAX_STEP_INTERVAL = 2000; // Maximum time between steps (ms)
            const MIN_ACCELERATION_VARIANCE = 0.3; // Minimum variance to detect movement

            // 6. Check for significant movement variance
            if (movingAverageRef.current.length >= 5) {
              const variance = movingAverageRef.current.reduce((sum, val) => {
                const mean = movingAverageRef.current.reduce((a, b) => a + b, 0) / movingAverageRef.current.length;
                return sum + Math.pow(val - mean, 2);
              }, 0) / movingAverageRef.current.length;

              if (debugMode) {
                console.log(`Step Detection Debug - Variance: ${variance.toFixed(3)}, Threshold: ${MIN_ACCELERATION_VARIANCE}, Acceleration: ${smoothedAcceleration.toFixed(3)}`);
              }

              // If there's not enough movement variance, don't count steps
              if (variance < MIN_ACCELERATION_VARIANCE) {
                if (debugMode) {
                  console.log('Step rejected: insufficient movement variance');
                }
                return;
              }
            }

            // 7. Peak detection with timing constraints
            if (smoothedAcceleration > STEP_THRESHOLD) {
              const timeSinceLastStep = now - lastStepTimeRef.current;
              const timeSinceLastPeak = now - lastPeakTimeRef.current;

              // Only count as step if:
              // - Enough time has passed since last step
              // - Not too much time has passed (indicates continuous movement)
              // - Peak is significant enough
              if (timeSinceLastStep > MIN_STEP_INTERVAL &&
                  (lastStepTimeRef.current === 0 || timeSinceLastStep < MAX_STEP_INTERVAL) &&
                  timeSinceLastPeak > 100) { // Avoid multiple peaks for same step

                lastPeakTimeRef.current = now;
                peakCountRef.current++;

                // Require consistent peaks before counting a step
                if (peakCountRef.current >= 1) {
                  lastStepTimeRef.current = now;
                  setSteps(s => {
                    if (debugMode) {
                      console.log(`Step counted! Total: ${s + 1}, Acceleration: ${smoothedAcceleration.toFixed(3)}, Time since last: ${timeSinceLastStep}ms`);
                    }
                    return s + 1;
                  });
                  peakCountRef.current = 0; // Reset peak counter
                }
              }
            }

            // 8. Reset peak counter if too much time has passed without movement
            if (now - lastPeakTimeRef.current > MAX_STEP_INTERVAL) {
              peakCountRef.current = 0;
            }
          });

          console.log('Accelerometer step tracking started successfully');
        } catch (listenerError) {
          console.log('Failed to start accelerometer listener, falling back to simulation:', listenerError);
          startSimulation();
        }
      } catch (e) {
        console.log('Failed to initialize accelerometer, falling back to simulation:', e);
        startSimulation();
      }
    };

    const stopAccelerometer = () => {
      try {
        if (accelSubRef.current) {
          if (typeof accelSubRef.current.remove === 'function') {
            accelSubRef.current.remove();
          } else if (typeof accelSubRef.current === 'function') {
            // older listener shapes
            accelSubRef.current();
          }
          console.log('Accelerometer listener stopped');
        }
      } catch (error) {
        console.log('Error stopping accelerometer listener:', error);
      }

      // Reset all detection state
      accelSubRef.current = null;
      gravityRef.current = 0;
      movingAverageRef.current = [];
      peakCountRef.current = 0;
      lastPeakTimeRef.current = 0;
      lastStepTimeRef.current = 0;
    };

    if (isTracking) {
      // attempt accelerometer first
      startAccelerometer();
    } else {
      // stop any running tracking/subscriptions
      stopAccelerometer();
      stopSimulation();
    }

    return () => {
      mounted = false;
      stopAccelerometer();
      stopSimulation();
    };
  }, [isTracking]);

  const startTracking = () => setIsTracking(true);
  const stopTracking = () => setIsTracking(false);
  const resetSteps = () => {
    setSteps(0);
    // Reset detection state
    gravityRef.current = 0;
    movingAverageRef.current = [];
    peakCountRef.current = 0;
    lastPeakTimeRef.current = 0;
    lastStepTimeRef.current = 0;
  };

  return {
    steps,
    isTracking,
    startTracking,
    stopTracking,
    resetSteps,
    permissionStatus,
    requestPermissions,
  };
}