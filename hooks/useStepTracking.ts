import { useState, useEffect, useRef } from 'react';
import { useMotionPermissions } from './useMotionPermissions';

/**
 * Step tracking hook.
 *
 * Behavior:
 * - Requests motion permissions before starting tracking
 * - Attempts to use the device accelerometer (expo-sensors) to detect steps via a simple
 *   peak-detection/high-pass filter algorithm.
 * - If `expo-sensors` is not available or an error occurs, falls back to the previous
 *   simulated interval so the UI still shows progress for demos.
 *
 * Note: For production-grade step counting prefer native pedometer APIs
 * (HealthKit / Google Fit) or a well-tested library; this is a lightweight client-side
 * heuristic that works reasonably well for simple apps.
 */
export function useStepTracking() {
  const [steps, setSteps] = useState(0);
  const [isTracking, setIsTracking] = useState(false);
  const [permissionRequested, setPermissionRequested] = useState(false);

  const { permissionStatus, requestPermissions } = useMotionPermissions();

  const accelSubRef = useRef<any>(null);
  const gravityRef = useRef<number>(0);
  const lastStepTimeRef = useRef<number>(0);
  const simIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    let mounted = true;

    const startSimulation = () => {
      if (simIntervalRef.current) return;
      simIntervalRef.current = setInterval(() => {
        // keep the old demo behaviour as a fallback
        setSteps(prev => prev + Math.floor(Math.random() * 5));
      }, 2000);
    };

    const stopSimulation = () => {
      if (simIntervalRef.current) {
        clearInterval(simIntervalRef.current as any);
        simIntervalRef.current = null;
      }
    };

    const startAccelerometer = async () => {
      try {
        // Check and request permissions first
        if (!permissionStatus.granted && !permissionRequested) {
          setPermissionRequested(true);
          const newPermissionStatus = await requestPermissions();
          if (!newPermissionStatus.granted) {
            console.log('Motion permission denied, falling back to simulation');
            startSimulation();
            return;
          }
        }

        // Check if we're on web - if so, skip accelerometer and use simulation
        if (typeof window !== 'undefined' && window.navigator) {
          console.log('Running on web, using simulation mode');
          startSimulation();
          return;
        }

        const accModule = await import('expo-sensors');
        const { Accelerometer } = accModule;

        if (!Accelerometer || typeof Accelerometer.addListener !== 'function') {
          console.log('Accelerometer not available, falling back to simulation');
          startSimulation();
          return;
        }

        // Test if we can actually use the accelerometer
        try {
          // sample ~10Hz
          if (typeof Accelerometer.setUpdateInterval === 'function') {
            Accelerometer.setUpdateInterval(100);
          }

          accelSubRef.current = Accelerometer.addListener((data: { x: number; y: number; z: number }) => {
            if (!mounted) return;
            const { x, y, z } = data;
            const mag = Math.sqrt(x * x + y * y + z * z);

            // simple low-pass filter to estimate gravity
            const alpha = 0.9;
            gravityRef.current = alpha * gravityRef.current + (1 - alpha) * mag;

            // high-pass signal (acceleration without gravity)
            const highPass = mag - gravityRef.current;

            const now = Date.now();

            // heuristic thresholds; may need tuning per device
            const STEP_THRESHOLD = 0.7; // g's above gravity
            const MIN_STEP_INTERVAL = 400; // ms

            if (highPass > STEP_THRESHOLD && (now - lastStepTimeRef.current) > MIN_STEP_INTERVAL) {
              lastStepTimeRef.current = now;
              setSteps(s => s + 1);
            }
          });

          console.log('Accelerometer step tracking started successfully');
        } catch (listenerError) {
          console.log('Failed to start accelerometer listener, falling back to simulation:', listenerError);
          startSimulation();
        }
      } catch (e) {
        console.log('Failed to initialize accelerometer, falling back to simulation:', e);
        startSimulation();
      }
    };

    const stopAccelerometer = () => {
      try {
        if (accelSubRef.current) {
          if (typeof accelSubRef.current.remove === 'function') {
            accelSubRef.current.remove();
          } else if (typeof accelSubRef.current === 'function') {
            // older listener shapes
            accelSubRef.current();
          }
          console.log('Accelerometer listener stopped');
        }
      } catch (error) {
        console.log('Error stopping accelerometer listener:', error);
      }
      accelSubRef.current = null;
    };

    if (isTracking) {
      // attempt accelerometer first
      startAccelerometer();
    } else {
      // stop any running tracking/subscriptions
      stopAccelerometer();
      stopSimulation();
    }

    return () => {
      mounted = false;
      stopAccelerometer();
      stopSimulation();
    };
  }, [isTracking]);

  const startTracking = () => setIsTracking(true);
  const stopTracking = () => setIsTracking(false);
  const resetSteps = () => setSteps(0);

  return {
    steps,
    isTracking,
    startTracking,
    stopTracking,
    resetSteps,
    permissionStatus,
    requestPermissions,
  };
}